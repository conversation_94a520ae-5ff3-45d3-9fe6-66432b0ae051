using UnityEngine;
using UnityEngine.Tilemaps;

public class MapScene : MonoBehaviour
{
    public GameObject mapGo;

    [Header("拖动设置")]
    public float dragSpeed = 2f;
    public float boundaryPadding = 1f;

    private Camera mainCamera;
    private Tilemap tilemap;
    private Grid grid;
    private bool isDragging = false;
    private Vector3 lastMousePosition;

    private BoundsInt tilemapBounds;
    private Bounds worldBounds;

    void Start()
    {
        InitializeComponents();
        CalculateBounds();
    }

    void Update()
    {
        HandleInput();
    }

    void InitializeComponents()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
            mainCamera = FindObjectOfType<Camera>();

        if (mapGo != null)
        {
            grid = mapGo.GetComponent<Grid>();
            tilemap = mapGo.GetComponentInChildren<Tilemap>();
        }
    }

    void CalculateBounds()
    {
        if (tilemap == null) return;

        tilemap.CompressBounds();
        tilemapBounds = tilemap.cellBounds;

        Vector3 minWorld = grid.CellToWorld(tilemapBounds.min);
        Vector3 maxWorld = grid.CellToWorld(tilemapBounds.max);

        worldBounds = new Bounds();
        worldBounds.SetMinMax(minWorld, maxWorld);

        worldBounds.Expand(boundaryPadding);
    }

    void HandleInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            StartDrag();
        }
        else if (Input.GetMouseButton(0) && isDragging)
        {
            UpdateDrag();
        }
        else if (Input.GetMouseButtonUp(0))
        {
            EndDrag();
        }
    }

    void StartDrag()
    {
        isDragging = true;
        lastMousePosition = Input.mousePosition;
    }

    void UpdateDrag()
    {
        Vector3 currentMousePosition = Input.mousePosition;
        Vector3 mouseDelta = lastMousePosition - currentMousePosition;

        Vector3 worldDelta = mainCamera.ScreenToWorldPoint(new Vector3(mouseDelta.x, mouseDelta.y, mainCamera.nearClipPlane));
        worldDelta = worldDelta - mainCamera.ScreenToWorldPoint(Vector3.zero);

        Vector3 newPosition = mainCamera.transform.position + worldDelta * dragSpeed;

        newPosition = ClampCameraPosition(newPosition);

        mainCamera.transform.position = newPosition;
        lastMousePosition = currentMousePosition;
    }

    void EndDrag()
    {
        isDragging = false;
    }

    Vector3 ClampCameraPosition(Vector3 targetPosition)
    {
        if (mainCamera == null) return targetPosition;

        float cameraHeight = mainCamera.orthographicSize * 2f;
        float cameraWidth = cameraHeight * mainCamera.aspect;

        float minX = worldBounds.min.x + cameraWidth * 0.5f;
        float maxX = worldBounds.max.x - cameraWidth * 0.5f;
        float minY = worldBounds.min.y + cameraHeight * 0.5f;
        float maxY = worldBounds.max.y - cameraHeight * 0.5f;

        if (maxX < minX)
        {
            float centerX = (worldBounds.min.x + worldBounds.max.x) * 0.5f;
            minX = maxX = centerX;
        }

        if (maxY < minY)
        {
            float centerY = (worldBounds.min.y + worldBounds.max.y) * 0.5f;
            minY = maxY = centerY;
        }

        targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);

        return targetPosition;
    }

    public void RecalculateBounds()
    {
        CalculateBounds();
    }
}